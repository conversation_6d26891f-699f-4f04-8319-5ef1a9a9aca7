<!--
 * @Description: 判断题视图组件
 * @Author: qmzhang
 * @Date: 2024-05-13 17:47:21
 * @LastEditTime: 2025-07-07 11:19:00
 * @FilePath: \make-card\src\components\empty\QuesCardObject\Judge.view.vue
-->
<template>
  <div class="ques-item" :class="[`judge-item-${index}`, `judge-item--${optionBlock}`]" :qid="subItem.id">
    <div class="ques-sort" contenteditable="true" v-if="!subItem.parentId || isObjectiveMerged">
      {{ subItem.quesNos }}
    </div>
    <div class="ques-sort" contenteditable="true" v-else>{{ subItem.quesName }}
    </div>
    <div class="option-item click-element" :class="subItem.answer === 'A' ? 'full-color' : ''"
      @click="setAnswer(subItem, 'A')">
      <template v-if="optionBlock === 'rect'">
        <span title="正确" class="opttion-txt opttion-txt--judge" :class="{ 'iconfont': subItem.judgeType == 1 }"
          v-html="TFNameList[subItem.judgeType][0]"></span>
      </template>

      <template v-else>
        [<span title="正确" class="opttion-txt opttion-txt--judge" :class="{ 'iconfont': subItem.judgeType == 1 }"
          v-html="TFNameList[subItem.judgeType][0]"></span>]
      </template>
    </div>

    <div class="option-item click-element" :class="subItem.answer === 'B' ? 'full-color' : ''"
      @click="setAnswer(subItem, 'B')">
      <template v-if="optionBlock === 'rect'">
        <span title="错误" class="opttion-txt opttion-txt--judge" :class="{ 'iconfont': subItem.judgeType == 1 }"
          v-html="TFNameList[subItem.judgeType][1]"></span>
      </template>
      <template v-else>
        [<span title="错误" class="opttion-txt opttion-txt--judge" :class="{ 'iconfont': subItem.judgeType == 1 }"
          v-html="TFNameList[subItem.judgeType][1]"></span>]
      </template>
    </div>

    <!-- 补充选项：不显示，解决flex垂直后水平方向无法均匀排列问题 -->
    <template v-if="isObjectiveMerged">
      <div class="option-item option-item--fill click-element" v-for="oindex of 2" :key="oindex">
        [<span class="opttion-txt">A</span>]
      </div>
    </template>

  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import Paper from '@/views/paper';

export default defineComponent({
  props: {
    // 选项的显示方式 bracket|rect
    optionBlock: {
      type: String,
      default: "bracket",
    },

    index: {
      type: Number,
      default: 0,
    },

    // 子题干信息
    subItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  setup(props: any, ctx: any) {
    const state = reactive({
      Paper: Paper,
      // 客观题是否合并
      isObjectiveMerged: Paper.mergeQues,
      TFNameList: {
        1: ['&#xe953;', '&#xe952;'], // √ ×
        2: ['T', 'F'],
        3: ['A', 'B'],
      },
    });

    /**
     * @name 设置选择题答案
     * @param item :当前题
     * @param index:选项
     */
    const setAnswer = (item: any, answer: string) => {
      if (Paper.isEdit) {
        return;
      }
      ctx.emit('setAnswer', item, answer);
    };

    return {
      ...toRefs(state),
      setAnswer,
    };
  },
});
</script>

