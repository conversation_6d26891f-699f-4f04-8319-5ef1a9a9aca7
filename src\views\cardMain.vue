<template>
  <div class="page-box page-box--ques quescard noselect" :style='{
    "--fontSize": data.fontSize,
    "--fontFamily": data.fontFamily,
    "--lineHeight": data.space,
    width: data.pageLayout == IPAGELAYOUT.A33
      ? "130mm"
      : "192mm",
    "column-count": data.pageLayout == IPAGELAYOUT.A42 ? "2" : "unset",
    "column-gap": data.pageLayout == IPAGELAYOUT.A42 ? "10px" : "unset",
    "column-fill": isExecu ? "unset" : "balance"
  }'>
    <div class="header-box" :style='{ height: footerKeepHeight + "mm" }'>
      <page-header :position="getPosPointState(1)"></page-header>
    </div>
    <div class="hand-write-div" :style='{ "position": "absolute", "left": "-14mm" }'>
      <hand-write-area></hand-write-area>
    </div>
    <div class="header-info-box" :style='{ height: headerHeight + "mm", zIndex: 9 }'>
      <template v-if="data.numberLayout == INUMBERLAYOUT.WRITE">
        <page-header-info-write :page="1"></page-header-info-write>
      </template>
      <template v-else-if="data.numberLayout == INUMBERLAYOUT.QRCODE">
        <page-header-info-q-r-code :page="1"></page-header-info-q-r-code>
      </template>
      <template v-else-if="data.numberLayout == INUMBERLAYOUT.MORE">
        <page-header-info-more-no :page="1"></page-header-info-more-no>
      </template>
      <template v-else>
        <page-header-info v-if="data.pageLayout != IPAGELAYOUT.A33 && data.pageLayout != IPAGELAYOUT.A32"
          :page="1"></page-header-info>
        <page-header-info-a-33 v-else :page="1"></page-header-info-a-33>
      </template>
    </div>
    <template v-for="(item, index) in quesInfos" v-memo="[item.key,item.data,item.hideQuesSurface,data.ansToTopConfig.object,data.ansToTopConfig.fill]">
      <div v-if="item.showName == 1" class="ques-name-box"
        :style='{ "minHeight": data.space, "lineHeight": data.space, "clear": "both", "overflow": "hidden", "position": "relative" }'
        contenteditable='true' v-html="item.htmlname || item.name" @blur="((e) => { changeName(e, item) })"
        :id="'name_' + item.id" :key="item.id"></div>
      <template v-if="item?.doQuesList?.length">
        <div>请将所选题目对应的题号涂黑,并在对应题目区域作答,如果多涂,则按所涂的第一题计分。</div>
        <div v-for="doQues in item?.doQuesList" :id="'choose_' + doQues.ids.join('_')">
          <span>我选做的题目是（{{ doQues.ids.length }}选{{ doQues.doCount }}）</span>
          <span v-for="(name, ni) in doQues.name" :id="'choose_' + doQues.ids[ni]"
            style="line-height: 3mm;margin: 0 2mm;">[<span style="padding: 0px 1mm;margin: 0px;font-size: 3mm;">{{ name
              }}</span>]</span>
        </div>
      </template>
      <template v-if="(data.ansToTopConfig.fill && (QUES_TYPE.fill == item.typeId ||
        QUES_TYPE.fillEva == item.typeId)) || (data.ansToTopConfig.object && (QUES_TYPE.singleChoice == item.typeId ||
          QUES_TYPE.choice == item.typeId ||
          QUES_TYPE.judge == item.typeId))">
        <ques-card-fill v-if="QUES_TYPE.fill == item.typeId || QUES_TYPE.fillEva == item.typeId"
          :key="'QuesCardFill_' + item.id" :qId="item.id" :item="item" :index="index" :bigIndex="index"
          :hasTitle="item.hasTitle = false"></ques-card-fill>
        <empty-ques v-else :key="'EmptyQues_' + item.id" :qId="item.id" :item="item" :index="index" :bigIndex="index"
          :hasTitle="item.hasTitle = false"></empty-ques>
      </template>
      <template v-for="(sitem,sindex) in item.data" :key="sitem.id">
        <ques-item :item="sitem" :info="sitem" :index="index" :style="{
          'break-inside':data.pageLayout == IPAGELAYOUT.A42 ? 'avoid' : 'unset',
          'display': isHideQues(item,data,sindex) ? 'none' : 'block'
        }"></ques-item>
        <img-merge-list :data="data.groupImgData" :qid="sitem.id"></img-merge-list>
      </template>
    </template>

  </div>
</template>
<script>
import PageHeader from '@/components/PageHeader.vue';
import PageHeaderInfo from '@/components/PageHeaderInfo.vue';
import PageHeaderInfoA33 from '@/components/PageHeaderInfoA33.vue';
import PageHeaderInfoWrite from '@/components/PageHeaderInfoWrite.vue';
import PageHeaderInfoQRCode from '@/components/PageHeaderInfoQRCode.vue';
import PageHeaderInfoMoreNo from '@/components/PageHeaderInfoMoreNo.vue'
import HandWriteArea from '@/components/HandWriteArea.vue';
import PageFooter from '@/components/PageFooter.vue';
import QuesItem from '@/components/QuesItem.vue';
import EmptyQues from '@/components/empty/EmptyQues.vue';
import NoAnswerArea from '@/components/empty/NoAnswerArea.vue';
import QuesCardSubject from '@/components/empty/QuesCardSubject.vue';
import QuesCardFill from '@/components/empty/QuesCardFill.vue';
import ImgMergeList from '@/components/ImgMergeList.vue';
import {
  render,
  defineComponent,
  h,
  onMounted,
  onBeforeUnmount,
  onBeforeMount,
  reactive,
  watch,
  nextTick,
  computed,
  toRefs,
} from 'vue';
import { ICARDMODEL, ICorrectType, IPAGELAYOUT, INUMBERLAYOUT } from '@/typings/card';
import {
  getHeight,
  pxConversionMm,
  isElementVisible,
  reloadMathtex,
  hasSingleChildEachLevel,
} from '../utils/dom';
import { MARK_TYPE, QUES_TYPE } from '@/typings/card';
import { deepClone } from '@/utils/util';
import { PaperConstant } from './paper.constant';

import {
  pageHeight,
  footerKeepHeight,
  getHeaderInfoHeight,
} from '../utils/config';
import { arabToChinese } from '@/utils/util';
import bus from '@/utils/bus';
import { ElLoading } from 'element-plus';
import Paper from './paper';

export default defineComponent({
  components: {
    PageHeader,
    PageHeaderInfo,
    PageHeaderInfoA33,
    PageHeaderInfoWrite,
    PageHeaderInfoQRCode,
    PageHeaderInfoMoreNo,
    PageFooter,
    QuesCardFill,
    EmptyQues,
    QuesItem,
    QuesCardSubject,
    NoAnswerArea,
    HandWriteArea,
    ImgMergeList
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  setup(props, ctx) {
    const state = reactive({
      quesInfos: [],
      // 强制刷新key
      refreshKey: 0,
      isExecu: false,
      groupImgData: Paper.groupImgData
    });
    // 表头高度
    const headerHeight = computed(() => {
      //定义变量 以便计算属性可以进行监听
      let isSealLine = props.data.isSealLine;
      let numberLayout = props.data.numberLayout;
      let pageLayout = props.data.pageLayout;
      return getHeaderInfoHeight();
    });
    watch([() => props.data.space, () => props.data.fontSize], async () => {
      reloadCard(false);
    });
    watch([() => props.data.isSealLine], async () => {
      if (props.data.numberLayout == INUMBERLAYOUT.QRCODE) {
        reloadCard(false);
      }
    });
    watch([() => props.data.pageType], async () => {
      if (props.data.pageType) {
        mergeImg();
      } else {
        splitImg();
      }
    });

    const isHideQues = (item,data,index) => {
      return (([QUES_TYPE.choice, QUES_TYPE.judge, QUES_TYPE.singleChoice].includes(Number(item.typeId)) && data.ansToTopConfig.object) ||
      ([QUES_TYPE.fill,QUES_TYPE.fillEva].includes(Number(item.typeId)) && data.ansToTopConfig.fill)) && (item.hideQuesSurface == '1' || (item.hideSmallQues == '1' && index != 0));
    };

    const isMergeImgType = (typeId) => {
      return typeId == QUES_TYPE.choice || typeId == QUES_TYPE.judge || typeId == QUES_TYPE.singleChoice || typeId == QUES_TYPE.fill;
    };

    const mergeImg = () => {
      props.data.groupImgData.clear();
      state.quesInfos.forEach(item => {
        if (isMergeImgType(item.typeId)) {
          let qid = "";
          item.data.forEach(sitem => {
            if (isMergeImgType(sitem.typeId)) {
              const eles = document.querySelectorAll(`[id="${sitem.id}"]`);
              eles.forEach(ele => {
                // const imgList = ele.querySelectorAll('img');
                const imgList = [...ele.querySelectorAll('img')]
                  .filter(img => {
                    return (img.width > 60 && img.height > 60) && !$(img).parents('.q-r-o-item').length;
                  });

                imgList.forEach((img, index) => {
                  const scale = img.height / 100;
                  const imgObj = {
                    src: img.src,
                    width: img.width / scale,
                    height: 100,
                    quesId: sitem.id,
                    quesName: sitem.quesNos,
                    sort: imgList.length > 1 ? (index + 1) : ''
                  }
                  $(img).remove();
                  // img.style.display = "none";
                  sitem.isMergeImg = true;
                  if (qid == "") {
                    qid = sitem.id;
                    props.data.groupImgData.set(qid, { h: 100, list: new Array(imgObj) });
                  } else {
                    let width = props.data.groupImgData.get(qid).list.reduce((pre, cur) => {
                      return pre + Number(cur.width);
                    }, 0)
                    width = pxConversionMm(width + imgObj.width);
                    const maxWidth = props.data.pageLayout == IPAGELAYOUT.A33 ? 120 : 180;
                    if (width < maxWidth) {
                      props.data.groupImgData.get(qid).list.push(imgObj);
                    } else {
                      qid = sitem.id;
                      props.data.groupImgData.set(qid, { h: 100, list: new Array(imgObj) });
                    }
                  }
                })
              });
            }
          })
        }
      })
      reloadCard(false);
    }

    const splitImg = () => {
      Array.from(props.data.groupImgData).forEach((group) => {
        group[1].list.forEach(img => {
          let imgEle = document.createElement('img');
          imgEle.src = img.src;
          imgEle.width = img.width;
          imgEle.height = img.height;
          let els = document.querySelectorAll(`[id="${img.quesId}"]`);
          //还原原题图片
          let content = $('.tmpcontent,.hascontent,.ques-content', els[0]);
          if (!content.length) {
            content = $('.tmpcontent,.hascontent,.ques-content', els[1]);
          }
          try {
            content[content.length - 1].append(imgEle);
          } catch (e) {
            content = $('.ques-box', els[0]).append(imgEle);
          }
          let ques = Paper.findSmallQuesByQuesId(img.quesId);
          ques.isMergeImg = false;
        })
      })
      Paper.notifyUpdateData('right');
      props.data.groupImgData.clear();
      reloadCard(false);
    }

    const handleReRenderMathJax = async () => {
      //重置高度，避免切换布局模式高度重叠
      mathjaxLoad(async () => {
        await nextTick();
        renderCard();
        renderOptions();
      })
    };
    const reloadCard = async (isReset = false) => {
      mergeDom();
      removeDom();
      state.quesInfos = Paper.quesInfosInCard;
      await nextTick();
      Paper.trigger('changeLayout', isReset ? 0 : Paper.pageLayout);
      setTimeout(() => {
        mathjaxLoad(async () => {
          renderCard();
          renderOptions();
        });
      }, 500);
    };

    const handleChangeNumberLayout = from => {
      // 更改考号类型，手动触发刷新
      state.refreshKey++;
    };

    const removeStyle = () => {
      let el = document.body.getElementsByClassName('page-box')[0];
      for (let i = 0; i < el.children.length; i++) {
        if (el.children[i].className.indexOf('hand-write-div') >= 0) {
          continue;
        }
        el.children[i].style.width = '';
      }
    };

    /**
     * @name 序号转换
     * @param {题目序号} sort
     */
    const convertSort = sort => {
      return arabToChinese(sort);
    };

    /**
     * @name 获取定位点状态
     * @param {当前页码} page
     */
    const getPosPointState = page => {
      const step =
        [IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(props.data.pageLayout)
          ? 2
          : props.data.pageLayout === IPAGELAYOUT.A3
            ? 4
            : props.data.pageLayout === IPAGELAYOUT.A33
              ? 6
              : 5;
      let position = { left: true, right: true };
      if (props.data.pageLayout == IPAGELAYOUT.A3) {
        if (page % step === 1 || page % step === 3) {
          position = { left: true, right: false };
        } else if (page % step === 2 || page % step === 0) {
          position = { left: false, right: true };
        }
      } else if (props.data.pageLayout == IPAGELAYOUT.A33) {
        if (page % step === 1 || page % step === 4) {
          position = { left: true, right: false };
        } else if (page % step === 3 || page % step === 0) {
          position = { left: false, right: true };
        } else {
          position = { left: false, right: false };
        }
      } else if (props.data.pageLayout == IPAGELAYOUT.A32) {
        if (page % step === 1 || page % step === 4) {
          position = { left: true, right: false };
        } else if (page % step === 3 || page % step === 0) {
          position = { left: false, right: true };
        } else {
          position = { left: false, right: false };
        }
      } else if (props.data.pageLayout == IPAGELAYOUT.A23) {
        if (page % step === 1 || page % step === 3) {
          position = { left: true, right: false };
        } else if (page % step === 2 || page % step === 0) {
          position = { left: false, right: true };
        } else {
          position = { left: false, right: false };
        }
      }
      return position;
    };

    const getQuesContentH = el => {
      if (!el) return 0;
      let quesH = 0;
      Array.from(el?.children).forEach(ele => {
        if (!Array.from(ele.classList).includes('left')) {
          quesH += ele.offsetHeight;
        }
      });
      return pxConversionMm(quesH);
    };

    const replaceOtherClass = str => {
      return str.replace(/ |drag|over/g, '');
    };

    /**
     * @name 合并分割节点
     */
    const mergeDom = () => {
      const sqArr = document.querySelectorAll('.q-opt:not(.split-ques)');
      Array.from(sqArr).forEach(el => {
        let splitEls = document.querySelectorAll(`[id="${el.id}"]`);
        let sourceEl; //最后元素为原始标签
        splitEls = Array.from(splitEls).filter(item => {
          if (!item.className.includes("split-ques")) {
            sourceEl = item
          }
          return item.className.includes("split-ques")
        })
        if (!sourceEl) return;
        let sourceH = sourceEl.getElementsByClassName('ques-box')[0]?.offsetHeight || 0;
        sourceH = sourceH < 5 ? 0 : sourceH;

        if (!sourceEl.getElementsByClassName('ques-box')[0]?.offsetHeight) {
          Array.from(sourceEl.getElementsByClassName('ques-box')[0]?.children || []).forEach(el => {
            sourceH += el.offsetHeight;
          });
        }

        let quesH = 0;
        splitEls
          .reverse()
          .forEach(el => {
            quesH += el.offsetHeight;
            if (el.getElementsByClassName('score-table').length) {
              quesH = quesH - el.getElementsByClassName('score-table')[0].offsetHeight;
            }
            if (el.getElementsByClassName('ques-content-name').length) {
              quesH = quesH - el.getElementsByClassName('ques-content-name')[0].offsetHeight;
            }
            try {
              let tagEls = [];
              let score = el.getElementsByClassName('score-table');
              if (!score.length) {
                score = el.getElementsByClassName('write-score-box');
              }
              if (score.length) {
                tagEls.push(score[0].parentElement);
              }
              const ques = el.getElementsByClassName('ques-box');
              if (ques.length) {
                Array.from(ques[0].children).forEach(p => {
                  // if(p.nodeName == "DIV"){
                  //   Array.from(p.children).forEach((span) => {
                  //     tagEls.push(span)
                  //   })
                  // }else{
                  tagEls.push(p);
                  // }
                });
              }

              // if(tagEls.length==0){
              //   tagEls = el.getElementsByClassName("remove-ques")
              // }
              // let tagH = 0;
              for (let i = tagEls.length - 1; i >= 0; i--) {
                const tag = tagEls[i];
                // if(tag.className.indexOf('score-container')<0){
                //   // tagH += tag.offsetHeight;
                //   tagH = Math.max(tag.offsetHeight + tag.offsetTop,tagH);
                // }
                // tag.className = tag.className.replace("remove-ques", "");
                let tagParent = tag.parentElement;

                let target;
                //标记是否本级
                let isChilds = false;
                while (!target) {
                  if (sourceEl.getElementsByClassName(replaceOtherClass(tag.className)).length) {
                    //查找本级classname是否相同
                    target = sourceEl.getElementsByClassName(replaceOtherClass(tag.className))[0];
                    isChilds = true;
                  } else if (
                    //查找父级classname是否相同
                    sourceEl.getElementsByClassName(replaceOtherClass(tagParent.className)).length
                  ) {
                    target = sourceEl.getElementsByClassName(
                      replaceOtherClass(tagParent.className)
                    )[0];
                    isChilds = false;
                  } else if (tagParent.className.indexOf('ques-box') >= 0) {
                    target = sourceEl.getElementsByClassName('ques-box')[0];
                    isChilds = false;
                  }
                  tagParent = tagParent.parentElement;
                }
                if (isChilds) {
                  Array.from(tag.children)
                    .reverse()
                    .forEach(el => {
                      target.insertBefore(el, target.firstChild);
                    });
                } else {
                  target.insertBefore(tag, target.firstChild);
                }
              }
            } catch (error) {
              console.warn(error);
            }
          });
        const _h = Math.round(pxConversionMm(sourceH + quesH));
        sourceEl.getElementsByClassName('ques-box')[0]?.style.height &&
          (sourceEl.getElementsByClassName('ques-box')[0].style.height = _h ? _h + 'mm' : '');
      });
    };

    /**
     * @name 移除自定义插入的节点
     */
    const removeDom = () => {
      const ctArr = document.body.getElementsByClassName('custom-div');
      for (let i = ctArr.length - 1; i >= 0; i--) {
        //删除元素 元素.parentNode.removeChild(元素);
        if (ctArr[i] != null) ctArr[i].parentNode.removeChild(ctArr[i]);
      }

      const sqArr = document.body.getElementsByClassName('split-ques');
      for (let i = sqArr.length - 1; i >= 0; i--) {
        //删除元素 元素.parentNode.removeChild(元素);
        if (sqArr[i] != null) {
          bus.emit('update' + sqArr[i].id);
          //销毁跨页生成的编辑器
          let $dom = CKEDITOR.instances[`ck_${sqArr[i].id}_cp${sqArr[i].getAttribute('page')}`];
          $dom && $dom.destroy();
          sqArr[i].parentNode.removeChild(sqArr[i]);
        }
      }
      //避免公式渲染skipTags失效，造成公式重复，手动删除script标签
      Array.from(document.getElementsByClassName('ck-math-tex')).forEach(el => {
        const tag = el.getElementsByTagName('script')[0];
        tag && tag.parentNode.removeChild(tag);
      });
    };
    /**
     * @name 创建占位标签
     * @param {文档集} frag
     */
    const createEmpty = (height, isAll=false) => {
      // if(!height)return null;
      const emptyDiv = document.createElement('div');
      emptyDiv.className = 'custom-div empty';
      emptyDiv.style.height = height + 'mm';
      if(props.data.pageLayout == IPAGELAYOUT.A42 && isAll){
        emptyDiv.style.columnSpan = 'all';
      }
      return emptyDiv;
      // frag.appendChild(emptyDiv);
    };
    /**
     * @name 创建密封区
     * @param {当前页码} pageNum
     * @param {文档集} frag
     */
    const createHandWrite = (pageNum, frag) => {
      const handWriteDiv = document.createElement('div');
      handWriteDiv.style.position = 'absolute';
      handWriteDiv.style.left = '-14mm';
      handWriteDiv.className = 'custom-div hand-write-div';
      render(h(HandWriteArea, {}), handWriteDiv);
      return handWriteDiv;
    };
    /**
     * @name 创建页头标签
     * @param {当前页码} pageNum
     * @param {文档集} frag
     */
    const createHeader = (pageNum, frag) => {
      const headerDiv = document.createElement('div');
      headerDiv.style.height = footerKeepHeight + 'mm';
      if(props.data.pageLayout == IPAGELAYOUT.A42 ){
        headerDiv.style.columnSpan = 'all';
      }
      headerDiv.className = 'custom-div header-box';
      // frag.appendChild(headerDiv);
      render(
        h(PageHeader, {
          position: getPosPointState(pageNum),
        }),
        headerDiv
      );
      return headerDiv;
    };
    /**
     * @name 创建基础信息标签
     * @param {当前页码} pageNum
     * @param {文档集} frag
     */
    const createHeaderInfo = (pageNum, frag) => {
      const headerInfoDiv = document.createElement('div');
      headerInfoDiv.style.height = getHeaderInfoHeight() + 'mm';
      if(props.data.pageLayout == IPAGELAYOUT.A42 ){
        headerInfoDiv.style.columnSpan = 'all';
      }
      headerInfoDiv.className = 'custom-div header-info-box';
      let headerInfoComp;
      if (props.data.numberLayout == INUMBERLAYOUT.WRITE) {
        headerInfoComp = PageHeaderInfoWrite;
      } else if (props.data.numberLayout == INUMBERLAYOUT.QRCODE) {
        headerInfoComp = PageHeaderInfoQRCode;
      } else if(props.data.numberLayout == INUMBERLAYOUT.MORE) {
          headerInfoComp = PageHeaderInfoMoreNo;
      } else {
        if (props.data.pageLayout === IPAGELAYOUT.A33 || props.data.pageLayout === IPAGELAYOUT.A32) {
          headerInfoComp = PageHeaderInfoA33;
        } else {
          headerInfoComp = PageHeaderInfo;
        }
      }

      render(
        h(headerInfoComp, {
          page:
          [IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(props.data.pageLayout)
              ? pageNum
              : props.data.pageLayout == IPAGELAYOUT.A3
                ? Math.ceil(pageNum / 2)
                : props.data.pageLayout == IPAGELAYOUT.A33
                  ? Math.ceil(pageNum / 3)
                  : Math.ceil(pageNum / 2.5),
        }),
        headerInfoDiv
      );
      return headerInfoDiv;
    };

    const layoutToStep = {
      [IPAGELAYOUT.A4]: { step: 2, tagMap: ['反面', '正面'] },
      [IPAGELAYOUT.A42]: { step: 2, tagMap: ['反面', '正面'] },
      [IPAGELAYOUT.A3]: { step: 4, tagMap: ['反面2', '正面1', '正面2', '反面1'] },
      [IPAGELAYOUT.A33]: {
        step: 6,
        tagMap: ['反面3', '正面1', '正面2', '正面3', '反面1', '反面2'],
      },
      [IPAGELAYOUT.A32]: { step: 5, tagMap: ['反面2', '正面1', '正面2', '正面3', '反面1'] },
      [IPAGELAYOUT.A23]: {
        step: 5,
        tagMap: ['反面3', '正面1', '正面2', '反面1', '反面2'],
        adjustPage: pageNum => {
          let groupSize = pageNum % 5 <= 3 || pageNum % 5 != 0 ? 2.5 : 2;
          return Math.ceil(pageNum / groupSize);
        },
      },
    };

    /**
     * @name 创建页脚标签
     * @param {当前页码} pageNum
     * @param {文档集} frag
     */
    const createFooter = pageNum => {
      const layoutConfig = layoutToStep[props.data.pageLayout];
      if (!layoutConfig) {
        return;
      }

      const footer = document.createElement('div');
      footer.className = 'custom-div footer';
      footer.style.top = pageHeight * pageNum - footerKeepHeight + 'mm';

      const indexInStep = pageNum % layoutConfig.step;
      const pageTag = layoutConfig.tagMap[indexInStep] || '';
      const pageText = `第${Math.ceil(pageNum / layoutConfig.step)}张纸${pageTag}`;

      let page;
      if (props.data.pageLayout === IPAGELAYOUT.A32) {
        page =
          pageNum % layoutConfig.step === 3
            ? Math.ceil(pageNum / (layoutConfig.step / 2)) - 1
            : Math.ceil(pageNum / (layoutConfig.step / 2));
      } else if (layoutConfig.adjustPage) {
        page = layoutConfig.adjustPage(pageNum);
      } else {
        page = Math.ceil(pageNum / (layoutConfig.step / 2));
      }

      render(
        h(PageFooter, {
          page,
          totalPage: pageNum,
          pageText,
          position: getPosPointState(pageNum),
        }),
        footer
      );

      return footer;
    };

    const createLine = (pageNum, frag) => {
      const pageLine = document.createElement('div');
      pageLine.style.top = pageHeight * pageNum + 'mm';
      pageLine.className = 'custom-div page-line';
      // frag.appendChild(pageLine);
      return pageLine;
    };

    const renderCard = async el => {
      if (state.isExecu) {
        bus.emit('allCardRendered');
        console.log('-> allCardRendered')
        return;
      }
      state.isExecu = true;
      const scrollH = document.getElementsByClassName('content')[0].scrollTop;
      el = el || document.body.getElementsByClassName('page-box')[0];
      const step =
      [IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(props.data.pageLayout)
          ? 2
          : props.data.pageLayout === IPAGELAYOUT.A3
            ? 4
            : props.data.pageLayout === IPAGELAYOUT.A33
              ? 6
              : 5;
      mergeDom();
      removeDom();
      let height = 0;
      let pageSize = 1;
      let column1 = true;//是否两栏第一列
      const children = el.children;
      let pageMainHeight = pageHeight - footerKeepHeight;
      // if(props.data.pageLayout == IPAGELAYOUT.A42){
      //   pageMainHeight = pageMainHeight + (pageMainHeight - headerHeight.value - footerKeepHeight);
      // }
      for (let i = 0; i < children.length; i++) {
        if (children[i].className.indexOf('hand-write-div') >= 0) {
          continue;
        }
        let domHeight = getHeight(children[i]);
        // domHeight = Math.round(domHeight);
        // if (270 >= domHeight && domHeight >= 268) {
        //   domHeight = 269;
        // }
        if(props.data.pageLayout == IPAGELAYOUT.A42){
          if(children[i].classList.contains('header-info-box')||children[i].classList.contains('header-box')){
            children[i].style.columnSpan = 'all';
          }
        }
        if (children[i].className.indexOf('custom-div') < 0) {
          height += domHeight;
        }
        children[i].setAttribute('page', pageSize);
        if (props.data.pageLayout === IPAGELAYOUT.A32) {
          if ([1, 2, 3].includes(pageSize % 5)) {
            children[i].style.width = PaperConstant.WIDTH_A33;
          } else {
            children[i].style.width = PaperConstant.WIDTH_A4;
          }
        } else if (props.data.pageLayout === IPAGELAYOUT.A23) {
          if ([3, 4, 0].includes(pageSize % 5)) {
            children[i].style.width = PaperConstant.WIDTH_A33;
          } else {
            children[i].style.width = PaperConstant.WIDTH_A4;
          }
        } else {
          children[i].style.width = '';
        }
        //***** 冗余2mm的高度 避免高度精度误差 */
        if (height - 1 > pageMainHeight) {

          const curHeight = height - domHeight;
          height = 0;
          let prevHeight = 0;
          let scoreheight = 0;
          let nameheight = 0;
          const emptyHeight = pageMainHeight - curHeight;

          const calculateHeight = element => {
            let elheight = pxConversionMm(element.offsetHeight + element.offsetTop);
            if (element.children.length == 1 && element.children[0].tagName == "IMG" && element.children[0].style.position != "absolute") {
              //如果只有一个子级且为图形，则获取图片高度
              elheight = pxConversionMm(el.children[0].offsetHeight + element.offsetTop);
            }
            if (
              element.className.includes('score-table') ||
              (element.childElementCount == 1 &&
                element.getElementsByClassName('score-table').length)
            ) {
              //打分栏高度计算
              scoreheight = getHeight(element);
            } else if (
              Paper.cardType != ICARDMODEL.QUESCARD &&
              element.className.includes('ques-content-name')
            ) {
              //纯答题卡题目名称高度计算
              nameheight = elheight;
            } else {
              elheight = scoreheight + elheight + nameheight;
            }
            if (elheight <= emptyHeight) {
              element.classList.add('remove-ques');
              prevHeight = Math.max(prevHeight, elheight);
            } else {
              if (emptyHeight - prevHeight < 3) {
                return true;
              } else {
                if (
                  element.nodeName == 'BR' ||
                  (element.children.length &&
                    !Array.from(element.classList).includes('q-r-option') &&
                    !Array.from(element.classList).includes('merge-img') &&
                    !Array.from(element.classList).includes('writing-line') &&
                    !Array.from(element.classList).includes('score-container') &&
                    !Array.from(element.classList).includes('ck-math-tex') &&
                    !Array.from(element.classList).includes('ques-item') &&
                    !Array.from(element.classList).includes('ques-name-box') &&
                    element.nodeName != 'TABLE' &&
                    !Array.from(element.childNodes).some(
                      node => node.nodeType === Node.TEXT_NODE && node.textContent.trim() !== ''
                    ) &&
                    !(
                      (element.nodeName == 'P' || element.nodeName == 'SPAN') &&
                      (element.getElementsByTagName('sub').length ||
                        element.getElementsByTagName('sup').length ||
                        element.getElementsByTagName('u').length ||
                        element.getElementsByTagName('font').length ||
                        (hasSingleChildEachLevel(element) && (element.getElementsByTagName('br').length || element.getElementsByTagName('img').length)))
                    )) //标签内包含上下标不做拆分处理
                ) {
                  const childs = Array.from(element.children).filter(item => {
                    return (
                      (item.nodeType == Node.ELEMENT_NODE &&
                        (getHeight(item) || Array.from(item.classList).includes('score-container')) > 0 &&
                        !item.className.includes('nosave') &&
                        isElementVisible(item)) ||
                      item.nodeName == 'BR'
                    );
                  });

                  for (let j = 0; j < childs.length; j++) {
                    let _child = childs[j];
                    let skipCacul = _child.className.includes('btn-tool');

                    if (skipCacul || !calculateHeight(_child)) {
                      continue;
                    } else {
                      break;
                    }
                  }
                } else {
                  if (['IMG', 'BR', 'SPAN', 'TABLE', 'FONT'].includes(element.nodeName)) {
                    return true;
                  } else {
                    return false;
                  }
                }
              }
            }
          };

          if (props.data.spreadQues == 1 && children[i].className.indexOf('card') < 0) {
            // 跨多页 循环当前节点进行分页
            calculateHeight(children[i]);
          }

          let cpe = children[i].cloneNode(false);
          cpe.className += ' split-ques';
          cpe.className = cpe.className.replace('over', '');
          const buildDom = (eitem, cp) => {
            if (Array.from(cp.classList).includes('ques-box')) {
              cp.style.height = emptyHeight - scoreheight - nameheight + 'mm';
            }
            // 使用Array.from创建一个静态副本进行遍历，避免在遍历过程中修改集合
            Array.from(eitem.children).forEach(item => {
              
              if (item.querySelectorAll('.remove-ques').length) {
                let _cp = item.cloneNode(false);
                cp.appendChild(_cp);
                buildDom(item, _cp);
              } else if (item.className.indexOf('remove-ques') > -1) {
                // 先移除remove-ques类，再进行克隆，确保状态一致
                item.className = item.className.replace('remove-ques', '');
                let _cp = item.cloneNode(true);
                cp.appendChild(_cp);
                
                // 检查父元素是否存在且有子元素，再标记为remove
                if (item.parentElement && !item.nextSibling && (item.offsetHeight > 0 || hasSingleChildEachLevel(item.parentElement))) {
                  item.parentElement.classList.add('remove');
                }
                // 确保元素存在于DOM中再移除
                if (item.parentNode) {
                  item.remove();
                }
              }
            });
          };

          // 创建文档片段,用于存储新增的DOM节点,最后一次性插入DOM树
          const frag = document.createDocumentFragment();
          let quebH = 0;
          try {
            quebH = pxConversionMm(children[i].getElementsByClassName('ques-box')[0].offsetHeight);
          } catch (e) {
            quebH = pxConversionMm(children[i].offsetHeight);
          }
          // quebH = Math.round(quebH);
          const ch = pageHeight - footerKeepHeight * 2;
          //TODO
          if (prevHeight || quebH >= ch) {
            buildDom(children[i], cpe);
            //移除分割节点后为空的节点
            Array.from(children[i].getElementsByClassName('remove')).forEach(item => {
              if (item.className.indexOf('ques-box') < 0) {
                const parent = item.parentElement;
                if (parent.className.indexOf('ques-box') < 0 && parent.children.length == 1) {
                  parent.remove();
                }
                item.remove();
              }
            });
            frag.appendChild(cpe);
            try {
              let h = quebH;
              if (emptyHeight > scoreheight) {
                h = h - (emptyHeight - scoreheight);
              }
              if (
                quebH < pageMainHeight &&
                ![IPAGELAYOUT.A32, IPAGELAYOUT.A23].includes(props.data.pageLayout)
              ) {
                h = Math.max(getQuesContentH(children[i].getElementsByClassName('ques-box')[0]), h);
              } else {
                h = Math.max(5, h);
              }
              h = Math.round(h);
              children[i].getElementsByClassName('ques-box')[0].style.height &&
                (children[i].getElementsByClassName('ques-box')[0].style.height = h + 'mm');
            } catch (error) {
              console.error('跨页高度设置异常，此处标记', error);
            }
          } else {
            if (quebH >= ch && emptyHeight <= ch) {
              console.error('页面内容超过高度，此处标记');
              children[i].getElementsByClassName('ques-box')[0].style.height = quebH - ch + 'mm';
            }else{
              if(props.data.pageLayout == IPAGELAYOUT.A42){
                height = quebH;
              }
            }
          }
          if (props.data.pageLayout == IPAGELAYOUT.A42) {
            if(column1){
              if(pageSize % 2 === 1){
                height += headerHeight.value + footerKeepHeight;
              }else{
                // let moreH = quebH - emptyHeight;
                // if(moreH > 0){
                //   height += moreH + footerKeepHeight;
                // }
                height = footerKeepHeight;
              }
              column1 = false;
              children[i].parentElement.insertBefore(frag, children[i]);
              continue;
            }else{
              height = 0;
              column1 = true;
            }
          }
          // 底部占位空元素  如果分页包含内容区域，只增加页脚高度空白
          if (cpe.getElementsByClassName('ques-box').length) {
            frag.appendChild(createEmpty(footerKeepHeight,true));
          } else {
            if (props.data.pageLayout == IPAGELAYOUT.A42) {
              // frag.appendChild(
              //   createEmpty(
              //     Math.min(pageMainHeight - curHeight - prevHeight, pageMainHeight),false
              //   )
              // );
              frag.appendChild(
                createEmpty(footerKeepHeight,true)
              );
            } else {
              frag.appendChild(
                createEmpty(
                  Math.min(pageMainHeight - curHeight - prevHeight + footerKeepHeight, pageMainHeight)
                )
              );
            }

          }

          el.appendChild(createFooter(pageSize));
          el.appendChild(createLine(pageSize));
          pageSize++;
          frag.appendChild(createHeader(pageSize));
          height += footerKeepHeight;
          if (
            ([IPAGELAYOUT.A4,IPAGELAYOUT.A42].includes(props.data.pageLayout) && pageSize % 2 === 1) ||
            (props.data.pageLayout == IPAGELAYOUT.A3 && pageSize % 4 === 1) ||
            (props.data.pageLayout == IPAGELAYOUT.A33 && pageSize % 6 === 1) ||
            (props.data.pageLayout == IPAGELAYOUT.A32 && pageSize % 5 === 1) ||
            (props.data.pageLayout == IPAGELAYOUT.A23 && pageSize % 5 === 1)
          ) {
            frag.appendChild(createHandWrite(pageSize));
            frag.appendChild(createHeaderInfo(pageSize));
            height += getHeaderInfoHeight();
          }

          children[i].parentElement.insertBefore(frag, children[i]);
        }
      }

      const frag = document.createDocumentFragment();
      //判断最后一个元素是否填满单页，未填满则增加空白面，保证定位点
      let _step =
        props.data.pageLayout === IPAGELAYOUT.A23
          ? 2
          : props.data.pageLayout === IPAGELAYOUT.A32
            ? 3
            : step / 2;
      if ((pageSize % step) % _step) {
        //创建文档片段,用于存储新增的DOM节点,最后一次性插入DOM树
        //占位空元素
        frag.appendChild(createEmpty(pageHeight - height));
        el.appendChild(createFooter(pageSize));
        el.appendChild(createLine(pageSize));
        while ((pageSize % step) % _step) {
          pageSize++;
          frag.appendChild(createHeader(pageSize));
          frag.appendChild(createEmpty(pageMainHeight));
          el.appendChild(createFooter(pageSize));
          el.appendChild(createLine(pageSize));
        }
      } else {
        //占位空元素
        if(props.data.pageLayout == IPAGELAYOUT.A42){
          debugger
          if(column1){
            //如果A4两栏第一栏结尾，增加栏目对应空白页，避免布局塌陷
            let emptyH = 0
            if(pageSize % 2 === 1){
                emptyH = headerHeight.value + footerKeepHeight;
              }else{
                emptyH = footerKeepHeight;
              }
              frag.appendChild(createEmpty(pageHeight - height - footerKeepHeight));
              frag.appendChild(createEmpty(pageHeight - emptyH - footerKeepHeight));
          }else{
            frag.appendChild(createEmpty(pageHeight - height - footerKeepHeight));
          }
          // frag.appendChild(createEmpty(footerKeepHeight,true));
        }else{
          frag.appendChild(createEmpty(pageHeight - height));
        }
        el.appendChild(createFooter(pageSize));
        el.appendChild(createLine(pageSize));
      }
      children[children.length - 1].parentElement.appendChild(frag);
      let totalPage = 0;
      totalPage =
        Number($('.footer-content .page:last')?.text()) || Math.ceil(pageSize / (step / 2));
      props.data.pageCount = totalPage;
      //统一更改页码总数
      Array.prototype.forEach.call(
        document.querySelectorAll('.footer-content .total'),
        function (ele) {
          ele.innerText = totalPage;
        }
      );
      el.style.height = pageSize * pageHeight + 'mm';
      document.getElementsByClassName('content')[0].scrollTop = scrollH;
      loadSplitQuesCke();

      await nextTick();
      bus.emit('allCardRendered');
      console.log('-> allCardRendered')
      state.isExecu = false;
    };

    // 加载跨页切分题目编辑器
    const loadSplitQuesCke = () => {
      if (Paper.isMathSubject() && $('.ck-math-tex').length > 100) return;
      //跨页题生成多个编辑器
      Array.from(document.getElementsByClassName('split-ques')).forEach(ques => {
        let edit = ques.getElementsByClassName('cke_editable')[0];
        if (edit) {
          edit.id = edit.id + '_cp' + ques.getAttribute('page');
          const editor = CKEDITOR.inline(edit);
          editor.on('contentDom', async function (event) {
            event.editor.element.removeAttribute('title');
            if (!props.data.isEnglishSubject()) {
              reloadMathtex(event.editor.element.$);
            }
          });
          editor.on('blur', function (e) {
            let el = document.getElementById('cke_ck_' + edit.id);
            el && (el.style.display = 'none');
            // renderCard();
          });
        }
      });
    };
    const handleUpdateQustions = async from => {
      //不通过vue绑定题目名称变化，避免题目重新渲染导致高度还原
      Paper.quesInfosInCard.forEach((ques) => {
        $('#name_' + ques.id).html(ques.htmlname || ques.name);
      })
      // if (from !== 'right') return;
      // state.quesInfos = Paper.quesInfosInCard;
      // // state.refreshKey++;
      // await nextTick();
      // //延迟分页，避免题目数据监听未更新导致异常
      // setTimeout(() => {
      //   renderCard();
      // }, 200);
    };

    const handleSwitchLayout = async params => {
      removeStyle();
      reloadCard(false);
    };

    // 设置题目数据
    const setQuesInfos = async () => {
      state.quesInfos = Paper.quesInfosInCard;
      setTimeout(() => {
        renderCard();
      }, 500);
      // reloadCard(true)
    };

    let loading = null;
    const handleOrderQuesInfos = async () => {
      reloadCard(true);
    };

    const renderOptions = () => {
      if (props.data.pageLayout == IPAGELAYOUT.A32 || props.data.pageLayout == IPAGELAYOUT.A23) {
        setTimeout(() => {
          bus.emit('renderOpts');
          //异型卡延迟处理选项，避免宽度计算问题
        }, 2000);
      } else {
        bus.emit('renderOpts');
      }
    };

    const changeName = async (e, item) => {
      item.name = e.target.innerText;
      item.htmlname = e.target.innerHTML;
      // reloadCard(true)
    };

    const mathjaxLoad = (fn) => {
      if (props.data.isEnglishSubject()) {
        fn()
      } else {
        MathJax.Hub.Queue(['Typeset', MathJax.Hub], function () {
          fn()
        });
      }
    }

    /**
     * 延迟执行回调函数，直到指定条件成立。
     * @param {Function} condition 检查条件的函数，当条件返回true时，执行回调。
     * @param {Function} callback 条件满足时执行的回调函数。
     * @param {number} [interval=500] 检查条件的间隔时间，单位为毫秒。
     */
    const delayUntil = (condition, callback, interval = 500) => {
      const checkConditionAndExecute = () => {
        if (condition()) {
          clearInterval(intervalId);
          callback();
        }
      };

      const intervalId = setInterval(checkConditionAndExecute, interval);
      setTimeout(async () => {
        intervalId && clearInterval(intervalId);
      }, 15000);
    };

    onBeforeMount(() => {
      state.quesInfos = Paper.quesInfosInCard;
    });

    onBeforeUnmount(() => {
      Paper.off("updateQustions", handleUpdateQustions);
      Paper.off('changeNumberLayout', handleChangeNumberLayout);
      bus.off('renderAllCard', renderCard);
      bus.off('switchLayout', handleSwitchLayout);
      bus.off('updateQuesInfos', setQuesInfos);
      bus.off('orderQuesInfos', handleOrderQuesInfos);
      bus.off('reRenderMathJax', handleReRenderMathJax);
    });

    onMounted(() => {
      sessionStorage.setItem('queLoadCount', 0);
      Paper.on("updateQustions", handleUpdateQustions);
      Paper.on('changeNumberLayout', handleChangeNumberLayout);
      bus.on('reRenderMathJax', handleReRenderMathJax);
      bus.on('renderAllCard', renderCard);
      bus.on('switchLayout', handleSwitchLayout);
      bus.on('updateQuesInfos', setQuesInfos);
      bus.on('orderQuesInfos', handleOrderQuesInfos);

      $('.page-box').on('blur', '.q-item-box', function (e) {
        let qid = e.currentTarget.id;
        bus.emit("changeQuesHtml", qid)
      })

      loading = ElLoading.service({
        lock: true,
        text: '题目排版中...',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      if (props.data.cardType == ICARDMODEL.ONLYCARD && !props.data.quesInfos.length) {
        mathjaxLoad(() => {
          renderCard();
          // removeComment();
          loading.close();
        })
      } else {
        let isLoad = false;
        const initRender = () => {
          if (isLoad) return;
          isLoad = true;
          console.time('所有公式渲染完成');
          mathjaxLoad(() => {
            console.timeEnd('所有公式渲染完成');
            Paper.trigger('changeLayout', 0);
            setTimeout(() => {
              renderCard();
              renderOptions();
              // removeComment();
              loading.close();
            }, 200);
          })
        };
        //题目加载完成数与题目总数相等时执行
        const myCondition = () =>
          Number(sessionStorage.getItem('queLoadCount')) ==
          Object.keys(props.data.questions).length;
        delayUntil(
          myCondition,
          function () {
            initRender();
          },
          1000
        );
        //确保上面条件无法满足后，依然正常执行渲染分页 保底策略
        setTimeout(async () => {
          initRender();
        }, 15000);
      }
    });

    return {
      ...toRefs(state),
      isHideQues,
      convertSort,
      removeDom,
      renderCard,
      getPosPointState,
      IPAGELAYOUT,
      QUES_TYPE,
      INUMBERLAYOUT,
      headerHeight,
      getHeaderInfoHeight,
      setQuesInfos,
      changeName,
    };
  },
});
</script>

<style lang="scss">
.ck-powered-by {
  display: none;
}

.a33,
.a32,
.a23 {
  .page-box>div {
    width: 130mm;
  }
}

.page-box {
  position: relative;
  width: 196mm;
  //   width: 180mm;
  min-height: 297mm;
  height: auto;
  margin: 0 auto;
  // padding: 0px 2mm;
  background: #fff;
  font-size: var(--fontSize);
  font-family: var(--fontFamily);
  line-height: var(--lineHeight);

  .footer {
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    height: 14mm;
    padding: 0;
    text-align: center;
  }

  .page-line {
    margin: 0;
    padding: 0;
    position: absolute;
    left: 0;
    height: 1px;
    width: 100%;
    background: #000;
    z-index: 2;
  }
}

.line-item.el-select {
  position: relative;

  .el-select-dropdown {
    position: absolute !important;
  }
}

.custom-div {
  position: relative;

  &.empty {
    position: relative;
    z-index: 100;
  }

  &.footer {
    z-index: 99;
  }

  &.splitor-empty {
    +.splitor-empty {
      display: none;
    }
  }
}

.fill-item-popover {
  padding: 0 !important;

  .el-popover__title {
    padding: 10px 15px 0;
    text-align: center;
  }
}
</style>
