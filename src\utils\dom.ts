/*
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2024-03-14 20:23:44
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-04-23 15:05:50
 */
import { PaperConstant } from "@/views/paper.constant";
import { pageHeight } from "./config";
import { IPAGELAYOUT } from "@/typings/card";

/**
 * 获取DPI
 * @returns {Array}
 */
export const conversion_getDPI = () => {
  const arrDPI = new Array();
  if ((window as any).screen.deviceXDPI) {
    arrDPI[0] = (window as any).screen.deviceXDPI;
    arrDPI[1] = (window as any).screen.deviceYDPI;
  } else {
    const tmpNode: any = document.createElement("DIV");
    tmpNode.style.cssText =
      "width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden";
    document.body.appendChild(tmpNode);
    arrDPI[0] = tmpNode.offsetWidth;
    arrDPI[1] = tmpNode.offsetHeight;
    tmpNode.parentNode.removeChild(tmpNode);
  }
  return arrDPI;
};
/**
 * px转换为mm
 * @param value
 * @returns {number}
 */
export const pxConversionMm = (value: any) => {
  if (!value || isNaN(value)) return 0;
  // 使用更精确的转换
  const inch = value / conversion_getDPI()[0];
  // 保留两位小数，避免浮点数精度问题
  const cValue = Math.round((inch * 25.4) * 100) / 100;

  return cValue;
};

/* 获取相应父节点 */
export const getParentNode = (dom: HTMLElement, classname: string): HTMLElement => {
  if (dom.className.includes(classname)) return dom;

  let pNode: HTMLElement = dom.parentElement;
  return pNode
}

/**
 * @description: 判断元素是否只有一个子元素
 * @param element
 * @return {*}
 */
export const hasSingleChildEachLevel = (element: HTMLElement) => {
  while (element && element.children.length === 1) {
    element = element.children[0] as HTMLElement; // 进入下一层
  }
  return !element || element.children.length === 0; // 最后一层要没有子元素
}

/**
 * mm转换为px
 * @param value
 * @returns {number}
 */
export const mmConversionPx = (value: number) => {
  if (!value || isNaN(value)) return 0;
  const inch = value / 25.4;
  const cValue = Math.round(inch * conversion_getDPI()[0]);
  return cValue;
};
window.mmConversionPx = mmConversionPx;
/**
 *获取元素高度
 */
export const getHeight = (el: any, unit: 'px' | 'mm' = 'mm') => {
  if (!el) return 0;
  let height = el.offsetHeight;
  if (el.children.length == 1 && el.children[0].tagName == "IMG" && el.children[0].style.position != "absolute") {
    //如果只有一个子级且为图形，则获取图片高度
    height = el.children[0].offsetHeight;
  }
  if (Number.isNaN(height) || height === 0) {
    height = el.getBoundingClientRect().height;
  }
  //兼容qq浏览器父级无高度样式情况下获取不到内部高度
  if (height == 0 && navigator.userAgent.indexOf("QQ") >= 0) {
    Array.from(el.children).forEach((e: any) => {
      height = e.offsetHeight + e.offsetTop;
    });
  }
  return unit === 'mm' ? pxConversionMm(height) : height;
};

export const getWidth = (el: any, unit: 'px' | 'mm' = 'mm') => {
  if (!el) return 0;
  let width = el.offsetWidth;
  if (Number.isNaN(width) || width === 0) {
    width = el.getBoundingClientRect().width;
  }
  if (width == 0 && navigator.userAgent.indexOf("QQ") >= 0) {
    Array.from(el.children).forEach((e: any) => {
      width += e.offsetWidth;
    });
  }
  return unit === 'mm' ? pxConversionMm(width) : width;
};

/**
 * @name: 判断元素可见性
 * @return: boolean
 */
export const isElementVisible = (ele: any): boolean => {
  try {
    return ele?.checkVisibility()
  } catch (e) {
    //兼容低版本不支持checkVisibility
    return ele && getWidth(ele) > 0 && getHeight(ele) > 0;
  }
}

/**
 * 获取客观题最小高度
 */
export const getObjectiveMinHeight = (domBoxs: Element[]): number => {
  // 遍历同等top的dom中最高的dom
  let minH = 0;
  let lastTop = -1, lastHeight = 0;
  domBoxs.forEach(($el: HTMLElement) => {
    let fullHeight = getFullHeight($el)
    if (lastTop === $el.offsetTop) {
      if (lastHeight < fullHeight) {
        lastHeight = fullHeight;
      }
    } else {
      lastTop = $el.offsetTop;
      lastHeight = fullHeight;
    }
  })
  minH = lastTop + lastHeight;

  return minH;
}

/**
 * 获取客观题最大高度
 */
export const getObjectiveMaxHeight = (domBoxs: Element[]): number => {
  let maxHeight = 0;
  domBoxs.forEach(($el: HTMLElement) => {
    let h = getHeight($el, 'px') + (getHeight($el, 'px') ? $el.offsetTop : 0);
    if (h < maxHeight) return;

    maxHeight = h;
  });

  return maxHeight
}


/**
 *获取元素完整高度，包含padding和margin
 */
export const getFullHeight = (element: any) => {
  // 获取计算后的样式
  var computedStyle = window.getComputedStyle(element);

  // 获取元素的高度、padding 和 margin
  var totalHeight =
    getAccHeight(element) +
    // parseFloat(computedStyle.paddingBottom) +
    parseFloat(computedStyle.marginBottom)

  return totalHeight
};

/* 获取精确高度 */
export const getAccHeight = (dom: any) => {
  if (!dom) return 0;

  return getHeight(dom, 'px');
}

export const getAccHeightByMM = (dom: any) => {
  return getHeight(dom);
}

/**
 * @name:计算dom坐标
 * @param el 获取坐标的Dom
 * @param zero 原点Dom
 */
export const getPoints = (el: any, zero: any) => {
  //原点坐标
  const initElement: any = document.getElementById(zero);
  const initPoint = initElement.getBoundingClientRect();
  //当前需要计算的坐标
  const currentDom: any = document.getElementById(el);
  const currentPoint: any = currentDom.getBoundingClientRect();
  const box = [
    getPxConversionMm(currentPoint.left - initPoint.left),
    getPxConversionMm(currentPoint.top - initPoint.top + initElement.scrollTop),
    getPxConversionMm(currentPoint.width),
    getPxConversionMm(currentPoint.height),
  ];
  return box;
};
/**
 * @name:计算分页dom坐标
 * @param el 获取坐标的Dom
 * @param zero 原点Dom
 */
export const getSplitPoints = (el: any, zero: any) => {
  //原点坐标
  const initElement: any = document.getElementById(zero);
  const initPoint = initElement.getBoundingClientRect();
  //当前需要计算的坐标
  // const currentDom: any = document.getElementById(el);
  const currentPoint: any = el.getBoundingClientRect();
  const box = [
    getPxConversionMm(currentPoint.left - initPoint.left),
    getPxConversionMm(currentPoint.top - initPoint.top + initElement.scrollTop),
    getPxConversionMm(currentPoint.width),
    getPxConversionMm(currentPoint.height),
  ];
  return box;
};
/**
 * @name:px转mm
 */
export const getPxConversionMm = (value: any) => {
  const conversion = (value * 1 * 25.4) / 96;
  return conversion;
};
/**
 * @name:获取当前在第几页
 * @param height 距离原点高度
 */
export const getCurrentPage = (height: any) => {
  const page = Math.ceil(height / pageHeight);
  return page;
};
export const handlePageLayout = (pageLayout: any, pageNum: any) => {
  let height = 0;
  const step = pageLayout === IPAGELAYOUT.A4 ? 2 : 4;
  if (pageLayout == IPAGELAYOUT.A4) {
    if (pageNum % step === 0) {
      height = 297;
    }
  } else {
  }
};

/**
 * @name:获取标题dom
 * @param pDom 父节点
 */
export const getNameElement = (pDom: HTMLElement): HTMLElement => {
  let nameEle: any = pDom.getElementsByClassName('ques-content-name');
  if (!nameEle.length) nameEle = pDom.getElementsByClassName('ques-content-name--small');

  return nameEle.length ? nameEle[0] : null;
}

export const reloadMathtex = (dom: HTMLElement) => {
  Array.from(dom.getElementsByClassName("ck-math-tex")).forEach(
    (item: HTMLElement) => {
      if (!item.innerText.trim()) {
        return;
      }
      const tex = item.getAttribute("tex");
      if (!tex) return;
      
      let span = document.createElement("span");
      span.className = "ck-math-tex";
      span.setAttribute("tex", tex);
      span.setAttribute("contenteditable", "false");
      span.innerHTML = tex;
      item.parentElement.replaceChild(span, item);
    }
  );
  setTimeout(() => {
    MathJax.Hub.Queue(["Typeset", MathJax.Hub, dom]);
  }, 20);
}

/**
 * @name 移除html注释内容
*/
export const removeComment = () => {
  // 创建一个NodeIterator，选择所有注释节点
  var nodeIterator = document.createNodeIterator(document, NodeFilter.SHOW_COMMENT, null);
  // 获取第一个注释节点
  var currentNode: any = nodeIterator.nextNode();
  // 循环遍历所有注释节点
  while (currentNode) {
    currentNode.remove()
    currentNode = nodeIterator.nextNode();
  }
}

/**
 * @description: 通过检测子元素高度来判断元素是否为空
 * @param {*} dom
 * @return {*}
 */
export function isChildDomEmpty($dom) {
  for (const $child of $dom.children) {
    if ($child.offsetHeight) return false
  }

  return true;
}


export default {
  getPxConversionMm,
  getPoints,
  getHeight,
  getWidth,
  isElementVisible,
  pxConversionMm,
  mmConversionPx,
  getSplitPoints,
  reloadMathtex,
  removeComment
};
