<!--
 * @Description: 选择题视图组件
 * @Author: qmzhang
 * @Date: 2024-05-13 17:46:53
 * @LastEditTime: 2024-11-20 15:30:44
 * @FilePath: \make-card\src\components\empty\QuesCardObject\Choice.view.vue
-->
<template>
  <div class="ques-item" :class="[`choice-item-${index}`, `choice-item--${optionBlock}`]" :qid="subItem.id">
    <div class="ques-sort" contenteditable="true" v-if="!subItem.parentId || (Paper.mergeQues && ALLOW_MIXINMERGE)">
      {{ subItem.quesNos }}
    </div>
    <div class="ques-sort" contenteditable="true" v-else>{{ subItem.quesName }}
    </div>

    <div class="option-item click-element" v-for=" oindex  of  Number(subItem.optionCount)" :key="oindex"
      :class="[subItem.answer.indexOf(az[oindex]) > -1 ? 'full-color' : '']" @click="setAnswer(subItem, oindex)">
      <template v-if="optionBlock === 'rect'">
        {{ az[oindex] }}
      </template>
      <template v-else>
        [<span class="opttion-txt">{{ az[oindex] }}</span>]
      </template>
    </div>

    <!-- 补充选项：不显示，解决flex垂直后水平方向无法均匀排列问题 -->
    <template v-if="Number(subItem.optionCount) < maxLength">
      <div class="option-item option-item--fill click-element" v-for="oindex of (maxLength - Number(subItem.optionCount))"
        :key="oindex">
        [<span class="opttion-txt">A</span>]
      </div>
    </template>

  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import Paper from '@/views/paper';
import { PaperConstant } from '@/views/paper.constant';

export default defineComponent({
  props: {
    // 选项的显示方式 bracket|rect
    optionBlock: {
      type: String,
      default: "bracket",
    },

    index: {
      type: Number,
      default: 0,
    },

    maxLength: {
      type: Number,
      default: 4,
    },

    // 题干信息
    subItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  setup(props: any, ctx: any) {
    const state = reactive({
      Paper: Paper,
      // 允许混合题合并
      ALLOW_MIXINMERGE: PaperConstant.ALLOW_MIXINMERGE,
      az: '0ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    });

    /**
     * @name 设置选择题答案
     * @param item :当前题
     * @param index:选项
     */
    const setAnswer = (item: any, index: any) => {
      if (Paper.isEdit) {
        return;
      }
      ctx.emit('setAnswer', item, index);
    };

    return {
      ...toRefs(state),
      setAnswer,
    };
  },
});
</script>

