<template>
    <div class="c30-fill-scores-modal">
        <el-dialog v-model="isShowDialog" title="填空题线上批改识别结果" width="900" :close-on-click-modal="false" @close="close"
            draggable>
            <div class="tip">红框表示小空批改区域，如需调整批改区域请在制卡时调整题干下划线位置，设置分数请点击修改。（系统自动将下划线长度≥8mm识别为作答区）</div>
            <div class="fill-score-warp">
                <template v-if="quesList?.length > 0">
                    <el-row class="fill-score-head">
                        <el-col v-if="Paper.cardType == ICARDMODEL.QUESCARD" :span="16">批改区域识别结果</el-col>
                        <el-col v-else :span="16">小题号</el-col>
                        <el-col :span="2">小题总分</el-col>
                        <el-col :span="2">小空分之和</el-col>
                        <el-col :span="4">小空分设置</el-col>
                    </el-row>
                    <template v-for="bques in quesList">
                        <el-row v-for="ques in squesList(bques)" class="fill-score-item">
                            <el-col v-if="Paper.cardType == ICARDMODEL.QUESCARD && !Paper.ansToTopConfig.fill"
                                :span="16">
                                <div class="html" :class="{ 'warn': ques.isWarn }" v-html="ques.html"></div>
                            </el-col>
                            <el-col v-else :span="16">{{ ques.quesNos }}</el-col>
                            <el-col :class="{ 'error': fillSumScore(ques) != ques.score }" :span="2">
                                <el-input style="width: 40px;" v-model="ques.score"
                                    @input="checkScore(ques)"></el-input>
                            </el-col>
                            <el-col :class="{ 'error': fillSumScore(ques) != ques.score }" :span="2">
                                {{ fillSumScore(ques) }}
                            </el-col>
                            <el-col v-if="ques.lineList?.length" class="line-score-input" :span="4">
                                <template v-for="(fill, index) in ques.lineList">
                                    <span style="margin-left: 4px;">空{{ index + 1 }}<el-input style="width: 35px;"
                                            v-model="fill.score" @input="checkScore(fill)"></el-input></span>
                                </template>
                                <span v-if="fillSumScore(ques) != ques.score" class="err-tip">※总分与小空分之和不等，请重新设置</span>
                            </el-col>
                            <el-col v-else :span="4">
                                <span class="err-tip">未识别到空，请在制卡时调整题干下划线</span>
                            </el-col>
                        </el-row>
                    </template>
                </template>
            </div>

            <template #footer>
                <div class="dialog-footer" style="text-align: center;">
                    <el-button @click="close">取消</el-button>
                    <el-button type="primary" @click="saveRules">保存</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { deepClone } from '@/utils/util';
import Paper from '../paper';
import { defineComponent, onBeforeMount, onMounted, computed, reactive, toRefs, watch } from 'vue'
import {
    ICARDMODEL,
} from '@/typings/card';
import { ElMessage } from 'element-plus';
const ITYPE = {
    NEW: 1,
    RESERT: 2,
    AVER: 3
}
export default defineComponent({
    props: {
        //弹框是否显示
        isShowDialog: {
            type: Boolean,
            default: false,
        },
        item: {
            type: Object,
            default: {}
        }
    },
    emits: ["close-dialog", "update-score-rules"],
    setup(props, ctx) {
        const state = reactive({
            ICARDMODEL: ICARDMODEL,
            Paper: Paper,
            quesList: []
        })
        // 填空题小空总分
        const fillSumScore = computed(() => (ques) => {
            return ques.lineList?.reduce((a, b) => {
                return (Math.round(a * 100 + Number(b.score) * 100) / 100)
            }, 0)
        });

        const squesList = computed(() => (bques) => {
            if (bques.children?.length)
                return bques.children
            if (bques.data?.length)
                return bques.data
            return [bques];
        })

        onBeforeMount(() => {
            state.quesList = deepClone(Array.isArray(props.item) ? props.item : (props.item.data ? props.item.data : [props.item]));
            if (Paper.cardType == ICARDMODEL.QUESCARD && !Paper.ansToTopConfig.fill) {
                state.quesList.forEach((item) => {
                    item.html = getQuesHtml(item.id)
                    if (/<\/u><u/g.test(item.html)) {
                        item.isWarn = true;
                    }
                })
            }
        })

        const getQuesHtml = (id) => {
            let els = document.querySelectorAll(`[id="${id}"]`);
            let html = '';
            Array.from(els)
                .forEach(el => {
                    let _el = el.cloneNode(true) as HTMLElement;
                    $(".score-container", _el).remove();
                    const curEle = _el.getElementsByClassName('ques-box')[0];
                    if (curEle) {
                        html += curEle.innerHTML;
                    }
                })
            return html;
        }

        const checkFillScore = () => {
            const errQues = state.quesList.find(ques => {
                if (ques.children?.length || ques.data?.length) {
                    const errSq = (ques.children || ques.data).find(sq => {
                        return sq.score != sq.lineList.reduce((a, b) => (Math.round(a * 100 + Number(b.score) * 100) / 100), 0) || !sq.lineList?.length;
                    })
                    if (errSq) {
                        return ques;
                    }
                } else {
                    return ques.score != ques.lineList.reduce((a, b) => (Math.round(a * 100 + Number(b.score) * 100) / 100), 0) || !ques.lineList?.length;
                }
            })
            return !errQues;
        }

        const checkScore = (row) => {
            let score = row.score;
            score = String(score).replace(/[^\d.]/g, '').replace(/\.{2,}/g, '');
            let parts = score.split('.');
            if (parts.length > 1) {
                parts = parts.splice(0, 2);
                // 如果有小数点
                parts[1] = parts[1].slice(0, 1); // 仅保留一位小数
            }
            score = parts.join('.');
            if (score > 100) {
                score = 100
            }
            row.score = score;
        }

        const saveRules = () => {
            if (!checkFillScore()) {
                ElMessage({
                    message: '设置有误，请核对设置项',
                    type: 'error',
                });
                return;
            }
            ctx.emit("update-score-rules", state.quesList)
            close()
        }

        const close = () => {
            ctx.emit("close-dialog");
        }

        return {
            ...toRefs(state),
            fillSumScore,
            squesList,
            checkScore,
            saveRules,
            close
        }
    },
})
</script>
<style lang="scss" scoped>
.c30-fill-scores-modal {
    .tip {
        padding: 5px 2px;
        font-size: 13px;
        color: red;
        background: rgba(255, 239, 189, 1);
    }

    .fill-score-warp {
        height: 500px;
        margin: 10px;
        overflow: auto;

        .fill-score-head {
            height: 36px;
            line-height: 36px;
            background: aliceblue;
        }

        .fill-score-item {
            line-height: 30px;
        }

        .el-col {
            // text-align: left;
            border-right: 1px solid #f3f3f3;
            border-bottom: 1px solid #f3f3f3;
            vertical-align: middle;
            display: flex;
            align-items: center;
            justify-content: center;

            &:first-child {
                border-left: 1px solid #f3f3f3;
            }

            .html {
                width: 100%;
                overflow: auto;
                padding: 5px;
            }

            .warn {
                border: 2px solid red;
                position: relative;

                &::before {
                    content: '请确认空数';
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    color: red;
                }
            }

            &.line-score-input {
                flex-wrap: wrap;
                justify-content: flex-start;
            }

            .err-tip {
                color: red;
                font-size: 12px;
            }

            &.error {
                color: red;
            }
        }
    }

    .el-dialog__body {
        border-bottom: 1px solid #d8d8d8 !important;
    }
}
</style>
<style lang="scss">
.c30-fill-scores-modal {
    .fill-score-item {
        counter-reset: item;
    }

    .fill-scan {
        counter-increment: item;
        border: 1px solid red;
    }

    .fill-scan::before {
        content: "空" counter(item);
        color: red;
        font-size: 12px;
    }
}
</style>
